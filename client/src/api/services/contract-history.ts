import type {
  ContractHistoryDetail,
  ContractHistoryListItem,
  HistoryQueryParams,
  UpdateHistoryTitleParams,
} from '@/types/api';
import type { PaginatedResponse, ResultResponse } from '@/types/common';

import api from '../api';

const API_PATH = '/contract/history';

/**
 * 合同历史记录API服务
 */
export const contractHistoryApi = {
  /**
   * 获取历史记录列表（支持分页和过滤）
   * @param params 查询参数
   * @returns 分页的历史记录列表
   */
  getHistoryList: async (
    params: HistoryQueryParams = {},
  ): Promise<ResultResponse<PaginatedResponse<ContractHistoryListItem>>> => {
    return api.post(`${API_PATH}`, params);
  },

  /**
   * 根据会话ID获取历史记录详情
   * @param sessionId 会话ID
   * @returns 历史记录详情
   */
  getHistoryDetail: async (sessionId: string): Promise<ResultResponse<ContractHistoryDetail>> => {
    return api.get(`${API_PATH}/${sessionId}`);
  },

  /**
   * 根据会话ID删除历史记录
   * @param sessionId 会话ID
   * @returns 删除结果
   */
  deleteHistory: async (sessionId: string): Promise<ResultResponse<void>> => {
    return api.delete(`${API_PATH}/${sessionId}`);
  },

  /**
   * 根据会话ID更新历史记录标题
   * @param sessionId 会话ID
   * @param params 更新参数
   * @returns 更新结果
   */
  updateHistoryTitle: async (
    sessionId: string,
    params: UpdateHistoryTitleParams,
  ): Promise<ResultResponse<ContractHistoryDetail>> => {
    return api.patch(`${API_PATH}/${sessionId}/title`, params);
  },

  /**
   * 根据会话ID下载合同docx文件
   * @param sessionId 会话ID
   * @returns 文件下载
   */
  downloadContract: async (sessionId: string): Promise<void> => {
    const response = await fetch(`/api/contract/download/${sessionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('下载失败');
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'contract.docx';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch) {
        filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
      }
    }

    // 下载文件
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  },
};
