import { type FC, memo, useEffect, useRef } from 'react';

import { CircleHelp } from 'lucide-react';

import { MarkdownEditor } from '@/components/MarkdownEditor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { type HomeController } from '../HomeController';

interface ContractPreviewProps {
  controller: HomeController;
}
const toolBars = [
  'bold',
  'underline',
  'italic',
  'strikeThrough',
  '-',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'task',
  '-',
  'link',
  'table',
  '-',
  'revoke',
  'next',
  '=',
  'prettier',
  'pageFullscreen',
  'preview',
  'previewOnly',
  'catalog',
];

const PreviewView: FC<ContractPreviewProps> = ({ controller }) => {
  const text = useObservableState(controller.contractText$);
  const ref = useRef<any>();

  const changeText = (val: string) => {
    controller.contractText$.next(val);
  };

  useEffect(() => {
    ref.current?.togglePreviewOnly?.(true);
  }, []);

  return (
    <MarkdownEditor
      className="size-full"
      value={text}
      noUploadImg
      noImgZoomIn
      noKatex
      noMermaid
      onChange={changeText}
      toolbars={toolBars as any}
      id="contract-markdown-editor"
      ref={ref}
    />
  );
};

const DownLoadWrap: FC<ContractPreviewProps> = memo(({ controller }) => {
  const downloading = useObservableState(controller.downloadContract);
});

export const ContractPreview: FC<ContractPreviewProps> = memo(({ controller }) => {
  const handleDownload = () => {
    controller.downloadContract();
  };

  const viewLog = () => {
    controller.toggleViewLogs(true);
  };

  const handleCompareWithTemplate = () => {
    controller.completeWithTemplate();
  };

  return (
    <Card className="size-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="flex items-center gap-2">
          合同预览
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <CircleHelp className="size-6" />
              </TooltipTrigger>
              <TooltipContent>
                当你需要编辑合同内容时，你可以点击右边第一个👁图标，退出预览模式即可编辑内容。
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleDownload}>
            下载合同
          </Button>
          <Button variant="outline" size="sm" onClick={handleCompareWithTemplate}>
            与模板比对
          </Button>
          <Button variant="outline" size="sm" onClick={viewLog}>
            查看日志
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-[calc(100%-80px)]">
        <div className="h-full overflow-y-auto bg-white">
          <PreviewView controller={controller} />
        </div>
      </CardContent>
    </Card>
  );
});

ContractPreview.displayName = 'ContractPreview';
