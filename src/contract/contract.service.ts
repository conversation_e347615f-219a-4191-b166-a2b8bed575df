import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { get, set } from 'lodash';
import { Observable, fromEvent } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { ResultResponse, ResultUtil } from '../common/utils/result.util';
import { parseStrToJson } from '../common/utils/stringUtil';
import { LlmManagerService } from '../llm/llm-manager.service';
import { PromptTemplateService } from '../prompt-template/prompt-template.service';

import { ContractHistoryService } from './contract-history.service';
import { ContractTemplatesService } from './contract-templates.service';
import { ContractTemplate, ContractTemplateSection } from './interfaces';

export interface CustomMessageEvent {
  data: string;
  type: string;
  id: string;
  retry?: number;
}

@Injectable()
export class ContractService {
  private readonly logger = new Logger(ContractService.name);
  private readonly model: string;
  private readonly llmPlatform: string;
  private readonly llmOptions: Record<string, any>;
  private readonly sseKey = 'contract.event';

  constructor(
    private readonly promptTemplateService: PromptTemplateService,
    private readonly llmManagerService: LlmManagerService,
    private readonly contractTemplatesService: ContractTemplatesService,
    private readonly contractHistoryService: ContractHistoryService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 默认 DeepSeek-32B
    this.model = this.configService.get<string>('LLM_MODEL', 'llm-reasoning-default');
    this.llmPlatform = this.configService.get<string>('LLM_PLATFORM', '');
    const llmOptionsStr = this.configService.get<string>('LLM_OPTIONS', '');
    this.llmOptions = parseStrToJson(llmOptionsStr, {});
    this.logger.log(`LLM_MODEL: ${this.model}`);
    this.logger.log(`LLM_PLATFORM: ${this.llmPlatform}`);
    this.logger.log(`LLM_OPTIONS: ${llmOptionsStr}`);
    this.logger.log('合同服务初始化成功');
  }

  /**
   * 创建SSE会话连接
   * @param sessionId 会话ID
   * @returns SSE Observable
   */
  public createSseSession(sessionId: string): Observable<CustomMessageEvent> {
    this.logger.log(`SSE会话已创建: ${sessionId}`);
    return fromEvent(this.eventEmitter, this.sseKey).pipe(
      filter((event: any) => event.sessionId === sessionId),
      map((event) => event.data),
      map((event) => {
        const eventKey = event.eventKey || 'message';
        const eventData = event.result || {};

        this.logger.debug(`发送SSE事件: ${eventKey}`, JSON.stringify(eventData));

        return {
          data: JSON.stringify(eventData),
          type: eventKey,
          id: Date.now().toString(),
          retry: 5000,
        };
      }),
    );
  }

  /**
   * 发送事件通知
   * @param sessionId 会话ID
   * @param eventKey 事件键
   * @param result 事件数据
   */
  public notifyEvent(sessionId: string, result: Record<string, any>, eventKey = 'message'): void {
    try {
      this.logger.debug(`准备发送事件到会话 ${sessionId}: ${eventKey}`);

      this.eventEmitter.emit(this.sseKey, { sessionId, data: { eventKey, result } });

      this.logger.debug(`成功发送事件到会话 ${sessionId}: ${eventKey}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`发送事件失败[${sessionId} ${eventKey}]: ${errorMessage}`, errorStack);
    }
  }

  /**
   * 向指定会话提交任务
   * @param sessionId 会话ID
   * @param userInput 用户输入
   * @param userId 用户ID
   * @returns 提交结果
   */
  public submitTaskToSession(sessionId: string, userInput: string, userId: number): ResultResponse {
    this.logger.log(`开始处理会话任务: ${sessionId}, 用户ID: ${userId}`);

    // 异步处理任务，不阻塞响应
    this.processContractGeneration(sessionId, userInput, userId);

    return { success: true, message: '任务已提交，正在处理中' };
  }

  /**
   * 处理合同生成任务
   * @param sessionId 会话ID
   * @param userInput 用户输入
   * @param userId 用户ID
   */
  private async processContractGeneration(
    sessionId: string,
    userInput: string,
    userId: number,
  ): Promise<void> {
    const logs: string[] = [];
    const logPush = (msg: string) => {
      logs.push(msg);
      this.notifyEvent(sessionId, { log: msg });
    };

    const result = await this.generateContractWithLogCallback(userInput, logPush);

    // 如果生成成功，保存历史记录
    if (result.success && result.data) {
      try {
        const historyResult = await this.contractHistoryService.saveHistory({
          userInput,
          backendLogs: logs.join('\n'),
          contractMarkdown: result.data.contract_markdown,
          selectedTemplateName: result.data.selected_template_name,
          templateMarkdown: result.data.template_markdown,
          userId,
          sessionId,
        });

        if (historyResult.success) {
          this.logger.log(
            `历史记录保存成功，会话ID: ${sessionId}, 用户ID: ${userId}, 记录ID: ${historyResult.data?.id}`,
          );
        } else {
          this.logger.error(
            `历史记录保存失败，会话ID: ${sessionId}, 用户ID: ${userId}, 错误: ${historyResult.message}`,
          );
        }
      } catch (error) {
        this.logger.error(`保存历史记录时发生异常，会话ID: ${sessionId}, 用户ID: ${userId}`, error);
      }
    }

    // 发送结果事件
    this.notifyEvent(sessionId, result);
  }

  // 生成合同流程
  private async generateContractWithLogCallback(
    userInput: string,
    onLog: (msg: string) => void,
  ): Promise<ResultResponse<any>> {
    return ResultUtil.execute(async () => {
      // 0. 加载所有模板摘要（内存获取）
      const templates = this.contractTemplatesService.getAllTemplates();
      if (!templates || !Array.isArray(templates) || templates.length === 0) {
        onLog('步骤0: 模板加载失败');
        return ResultUtil.fail('步骤0: 模板加载失败', 'TEMPLATE_LOAD_ERROR');
      }

      onLog('步骤1: 正在选择合适的合同模板...');
      const promptTemplateResp =
        await this.promptTemplateService.getLLmPrompt<Record<string, Record<string, string>>>();
      const promptTemplate = promptTemplateResp.success ? promptTemplateResp.data : {};

      // 1. 智能选择模板（LLM调用）
      const promptSelect = promptTemplate['template_selection'];
      if (!promptSelect) {
        onLog('步骤1-1: Prompt(template_selection)未配置');
        return ResultUtil.fail('步骤1-1: Prompt(template_selection)未配置', 'PROMPT_NOT_FOUND');
      }

      const requestOptions = this.llmPlatform ? {} : { headers: { 'x-model': this.model } };

      // 抽离总结信息
      const templatesSummary = templates.map(({ contract_metadata }) => contract_metadata);
      const prompt = promptSelect.user_prompt_template
        .replace('{{user_input}}', userInput)
        .replace('{{templates_summary}}', JSON.stringify(templatesSummary));
      const selectResult = await this.llmManagerService.callLLMUseOpenAISDK(
        prompt,
        { ...this.llmOptions, model: this.model, systemPrompt: promptSelect.system_message },
        requestOptions,
      );
      const selectContent = selectResult.data as string;
      if (!selectContent) {
        onLog('步骤1-2: 模板选择失败, LLM无响应');
        return ResultUtil.fail('步骤1-2: 模板选择失败, LLM无响应', 'TEMPLATE_SELECT_ERROR');
      }
      const selectedContractCode = this.extractContractCode(selectContent);
      if (!selectedContractCode) {
        onLog('步骤1-3: LLM未返回有效的模板文件名');
        return ResultUtil.fail('步骤1-2: LLM未返回有效的模板文件名', 'TEMPLATE_SELECT_ERROR');
      }

      // 方便调试
      this.logger.log(`AI选择的模版是: ${selectedContractCode}`);

      const templateData: ContractTemplate | undefined =
        this.contractTemplatesService.getTemplateByContractCode(selectedContractCode);
      if (!templateData) {
        onLog('步骤1-4: LLM返回的模板名文件不存在');
        return ResultUtil.fail('步骤1-4: LLM返回的模板名文件不存在', 'TEMPLATE_NOT_FOUND');
      }
      const tipName = templateData.contract_metadata.file_name.replace('.json', '');
      onLog(`步骤1：已成功匹配合同模板: ${tipName}`);

      // 2. 分组收集需要LLM处理的文本片段
      onLog('步骤2: 正在从模板中收集各逻辑块的文本内容...');
      // 获取提示词
      const contentPromptBlock = promptTemplate['content_processing'];
      const attachmentPromptBlock = promptTemplate['attachment_processing'];
      if (!contentPromptBlock) {
        onLog(`步骤2-0: 未配置content_processing prompt,将使用原始模板.`);
      }

      const copyData = JSON.parse(JSON.stringify(templateData));

      // 2-2. 按顺序调用LLM处理文本片段
      const orderKeys = contentPromptBlock
        ? ['metadata_block', 'main_content_block', 'standard_clauses_block', 'attachments_block']
        : [];

      for (const groupId of orderKeys) {
        const blockData = copyData[groupId];

        if (!blockData || Object.keys(blockData).length === 0) {
          onLog(`步骤2-2: 跳过空的或不存在的逻辑块: ${groupId}`);
          continue;
        }

        onLog(`步骤2-2: 正在处理逻辑块 '${groupId}' ...`);
        const blockJson = this.collectBlockJson(groupId, blockData);

        let promptBlockKey = '';
        let promptBlock: Record<string, string> = {};
        if (groupId === 'attachments_block') {
          promptBlock = attachmentPromptBlock;
          promptBlockKey = 'contract_attachment_block';
        } else {
          promptBlock = contentPromptBlock;
          promptBlockKey = 'contract_group_block';
        }
        const prompt = promptBlock.user_prompt_template
          .replace('{{user_input}}', userInput)
          .replace(`{{${promptBlockKey}}}`, JSON.stringify(blockJson));
        const llmBlockResult = await this.llmManagerService.callLLMUseOpenAISDK(
          prompt,
          {
            ...this.llmOptions,
            model: this.model,
            systemPrompt: promptBlock.system_message,
            parseJson: true,
          },
          requestOptions,
        );

        if (!llmBlockResult.success) {
          onLog(`步骤2-2: LLM返回内容失败, 将使用原始逻辑块:${groupId}`);
          continue;
        }

        this.applyProcessedBlock(groupId, blockData, llmBlockResult.data!, onLog);
        onLog(`步骤2-2: 逻辑块 '${groupId}' 处理完成。`);
      }

      // 3. 渲染为Markdown
      onLog('步骤3: 正在生成合同内容...');
      // AI修正过的合同
      const md1 = this.buildMarkdownFromJson(copyData);
      // 合同模板
      const md2 = this.buildMarkdownFromJson(templateData);
      onLog('步骤3: 合同内容生成成功。');

      return ResultUtil.success(
        { contract_markdown: md1, template_markdown: md2, selected_template_name: tipName },
        '合同已生成',
      );
    }, '合同生成失败');
  }

  private extractContractCode(content: string): string {
    return content.trim();
  }

  /**
   * 收集模板中需要LLM处理的文本片段
   */
  private collectBlockJson(blockId: string, blockData: any): Record<string, string> {
    const blockJson: Record<string, string> = {};
    if (blockId === 'metadata_block') {
      // 签约日期
      blockJson.signing_date = blockData.signing_date;
      (blockData.parties || []).forEach((party: any, index: number) => {
        const keys = Object.keys(party);
        keys.forEach((key) => (blockJson[`parties.${index}.${key}`] = party[key]));
      });
    }

    if (blockId === 'main_content_block' || blockId === 'standard_clauses_block') {
      blockJson.introduction = blockData.introduction || undefined;
      (blockData.sections || []).forEach((section: any, index: number) => {
        const spk = `sections.${index}`;
        blockJson[`${spk}.title`] = section.title || undefined;
        if (Array.isArray(section.content)) {
          section.content.forEach((c: string, ci: number) => {
            const lines = c.split('\n'); // 对其分割
            lines.forEach((l: string, li: number) => {
              blockJson[`${spk}.content.${ci}.lines.${li}`] = l;
            });
          });
        }
      });
    }

    if (blockId === 'attachments_block') {
      const sp = blockData.signature_page;
      if (sp) {
        if (sp.title) {
          blockJson[`signature_page.title`] = sp.title;
        }
        (sp.content || []).forEach((c: string, index: number) => {
          blockJson[`signature_page.content.${index}`] = c;
        });
      }
      (blockData.attachments || []).forEach((at: any, index: number) => {
        const key = `attachments.${index}`;
        const sp = at.signature_page;
        if (sp) {
          if (sp.title) {
            blockJson[`${key}.signature_page.title`] = sp.title;
          }
          (sp.content || []).forEach((c: string, index: number) => {
            blockJson[`${key}.signature_page.content.${index}`] = c;
          });
        }
        (at.content || []).forEach((c: string, ci: number) => {
          blockJson[`${key}.content.${ci}`] = c;
        });
        blockJson[`${key}.attachment_title`] = at.attachment_title || undefined;
        blockJson[`${key}.title`] = at.title || undefined;
      });
    }

    return blockJson;
  }

  private splitPathStrToArray(pathKey: string): any[] {
    return pathKey.split('.').map((k) => (isNaN(Number(k)) ? k : Number(k)));
  }

  /**
   * 递归回填LLM处理结果到模板
   */
  private applyProcessedBlock(
    blockId: string,
    blockData: any,
    processedBlock: any,
    onLog: (msg: string) => void,
  ) {
    for (const pathKey in processedBlock) {
      const value = processedBlock[pathKey];
      // 如果有lines,需要合并
      if (pathKey.includes('.lines.')) {
        const [parentPath, row] = pathKey.split('.lines.');
        const pps = this.splitPathStrToArray(parentPath);
        const lines = get(blockData, pps, '').split('\n');
        if (typeof value !== 'string' || value.trim().toLowerCase() === '[nochange]') continue;
        const oldValue = lines[row];
        // 有可能应为一些其他因素, 虽然文字未变，但依旧返回了值,这里做个二次判断确认;
        if (oldValue === value) continue;
        onLog(`步骤2-2: AI调整了合同条款内容: ${oldValue} -> ${value}`);
        lines[row] = value;
        set(blockData, pps, lines.join('\n'));
      } else {
        if (typeof value !== 'string' || value.trim().toLowerCase() === '[nochange]') continue;
        // 路径如 sections.0.content
        const pathArr = this.splitPathStrToArray(pathKey);
        const oldValue = get(blockData, pathArr);
        // 有可能应为一些其他因素, 虽然文字未变，但依旧返回了值,这里做个二次判断确认;
        if (oldValue === value) continue;
        onLog(`步骤2-2: AI调整了合同条款内容: ${oldValue} ---> ${value}`);
        set(blockData, pathArr, value);
      }
    }
  }

  /**
   * 渲染章节内容，处理层级关系
   */
  private renderSectionContent(content: string[]): string {
    let md = '';

    content.forEach((c: string) => {
      const lines = c.split('\n');

      let previousIndent = 0;
      lines.forEach((line) => {
        const trimmedLine = line.trim();

        if (!trimmedLine) {
          md += '\n';
          previousIndent = 0;
          return;
        }

        // 判断当前行的缩进级别
        const currentIndent = this.detectIndentLevel(trimmedLine, previousIndent);

        // 应用缩进并添加行
        md += ' '.repeat(currentIndent) + trimmedLine + '\n';
        previousIndent = currentIndent;
      });

      // 在每个content块之间添加空行
      md += '\n';
    });

    return md;
  }

  /**
   * 检测行的缩进级别
   */
  private detectIndentLevel(line: string, previousIndent: number): number {
    // 一级编号：数字.数字. (如 1.1., 2.3.)
    if (line.match(/^\d+\.\d+\./)) {
      return 0;
    }

    // 二级编号：(数字) (如 (1), (2))
    if (line.match(/^\(\d+\)/)) {
      return 2;
    }

    // 三级编号：(字母) (如 (a), (b))
    if (line.match(/^\([a-zA-Z]\)/)) {
      return 4;
    }

    // 四级编号：(中文数字) (如 (一), (二))
    if (line.match(/^\([一二三四五六七八九十百千万]+\)/)) {
      return 4;
    }

    // 五级编号：数字) (如 1), 2))
    if (line.match(/^\d+\)/)) {
      return 6;
    }

    // 六级编号：字母) (如 a), b))
    if (line.match(/^[a-zA-Z]\)/)) {
      return 8;
    }

    // 特殊格式：带引号的编号 (如 "投放平台"是指:)
    if (line.match(/^"[^"]+"\s*[是为]指[:：]/)) {
      return 2;
    }

    // 特殊格式：就...而言、关于...等开头
    if (line.match(/^就.+[而言|方面|事宜]/)) {
      return 2;
    }

    // 检查是否是纯数字开头的条款 (如 "1.甲方应当...")
    if (line.match(/^\d+\./)) {
      return 0;
    }

    // 检查是否是罗马数字 (如 I., II., III.)
    if (line.match(/^[IVX]+\./)) {
      return 0;
    }

    // 检查是否是中文数字开头 (如 一、二、三、)
    if (line.match(/^[一二三四五六七八九十百千万]+[、。]/)) {
      return 0;
    }

    // 如果没有匹配到编号格式，智能判断缩进
    return this.smartIndentDetection(line, previousIndent);
  }

  /**
   * 智能缩进检测
   */
  private smartIndentDetection(line: string, previousIndent: number): number {
    // 如果是明显的内容延续（不以编号开头），适当增加缩进
    const isContentContinuation = !line.match(/^[\d\(\)a-zA-Z"一二三四五六七八九十]/);

    if (isContentContinuation) {
      // 如果前一行是编号行，内容应该有适当缩进
      if (previousIndent < 8) {
        return Math.min(previousIndent + 2, 8);
      }
    }

    // 检查是否是分号结尾的条款（通常是列表项）
    if (line.endsWith(';') || line.endsWith('；')) {
      return Math.max(previousIndent, 2);
    }

    // 检查是否是句号结尾的完整条款
    if (line.endsWith('.') || line.endsWith('。')) {
      return previousIndent;
    }

    // 默认继承前一行缩进
    return previousIndent;
  }

  /**
   * 按新结构递归渲染Markdown - 美化合同样式
   */
  private buildMarkdownFromJson(data: any): string {
    let md = '';
    const meta = data.contract_metadata || {};
    const metaBlock = data.metadata_block || {};

    // 1. 合同标题（居中）
    if (metaBlock.contract_title) {
      md += `<div align="center">\n\n# ${metaBlock.contract_title}\n\n</div>\n\n`;
    } else if (meta.contract_type) {
      md += `<div align="center">\n\n# ${meta.contract_type}\n\n</div>\n\n`;
    }

    // 2. 当事人信息（格式美化）
    if (Array.isArray(metaBlock.parties)) {
      metaBlock.parties.forEach((party: any) => {
        const role = party['角色'] || '';
        const name = party['名称'] || '';

        if (role) {
          md += `**${role}**`;
          if (name) md += `：${name}`;
          md += '\n\n';

          // 其他信息缩进显示
          Object.keys(party).forEach((key) => {
            if (key === '角色' || key === '名称') return;
            const value = party[key] || '';
            md += `　　${key}：${value}\n\n`;
          });
        }
      });
      md += '\n\n';
    }

    // 3. 签约日期
    if (metaBlock.signing_date) {
      md += `**签订日期：** ${metaBlock.signing_date}\n\n`;
    }

    // 4. 主体内容
    const mainBlock = data.main_content_block || {};
    if (mainBlock.introduction) {
      md += `${mainBlock.introduction}\n\n`;
    }

    if (Array.isArray(mainBlock.sections)) {
      mainBlock.sections.forEach((section: ContractTemplateSection) => {
        if (section.title) {
          md += `## ${section.title}\n\n`;
        }
        if (section.introduction) {
          md += `${section.introduction}\n\n`;
        }
        if (Array.isArray(section.content)) {
          md += this.renderSectionContent(section.content);
        }
      });
    }

    // 5. 标准条款
    const stdBlock = data.standard_clauses_block || {};
    if (Array.isArray(stdBlock.sections)) {
      stdBlock.sections.forEach((section: any) => {
        if (section.title) {
          md += `## ${section.title}\n\n`;
        }
        if (Array.isArray(section.content)) {
          md += this.renderSectionContent(section.content);
        }
      });
    }

    // 6. 附件和签署区处理
    const attachmentsBlock = (data.attachments_block || {}) as Record<string, any>;

    // 6.1额外的内容
    (attachmentsBlock.extra_contents || []).forEach((content: string) => {
      if (content) {
        md += `${content}\n\n`;
      }
    });

    // 6.2处理签署区
    if (attachmentsBlock.signature_page) {
      // 使用 attachments_block 中的签署页
      if (attachmentsBlock.signature_page.title) {
        md += `### ${attachmentsBlock.signature_page.title}\n\n`;
      }
      (attachmentsBlock.signature_page.content || []).forEach((content: string) => {
        if (content) {
          md += `${content}\n\n`;
        }
      });
    }

    // 6.3处理附件
    (attachmentsBlock.attachments || []).forEach((attachment: any) => {
      // 附件标题处理
      if (attachment.attachment_title) {
        md += `# ${attachment.attachment_title}\n\n`;
      }

      if (attachment.title) {
        md += `### ${attachment.title}\n\n`;
      }

      // 附件内容处理
      (attachment.content || []).forEach((contentItem: string) => {
        if (contentItem) {
          md += `${contentItem}\n\n`;
        }
      });

      // 附件的签署页处理
      if (attachment.signature_page) {
        if (attachment.signature_page.title) {
          md += `### ${attachment.signature_page.title}\n\n`;
        }
        (attachment.signature_page.content || []).forEach((signContent: string) => {
          if (signContent) {
            md += `${signContent}\n\n`;
          }
        });
      }
    });

    return md;
  }
}
